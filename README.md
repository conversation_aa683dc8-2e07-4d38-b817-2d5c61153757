# OCR Screen Capture Extension

A powerful Chrome/Brave extension that allows you to select any area of a webpage, capture it, and extract text using OCR (Optical Character Recognition). Perfect for extracting text from images, videos, or any visual content on web pages.

## Features

- 🖱️ **Click and Drag Selection**: Draw rectangles or circles to select screen areas
- 📸 **Screen Capture**: Capture selected areas including images, videos, and any web content
- 🔍 **OCR Text Extraction**: Extract text and numbers using Tesseract.js
- 📋 **Auto Clipboard Copy**: Automatically copy extracted text to clipboard
- 🎯 **Multiple Selection Shapes**: Choose between rectangle and circle selection modes
- ⌨️ **Keyboard Shortcuts**: Quick activation and control via keyboard
- 🎨 **Visual Feedback**: Real-time selection preview with smooth animations

## Installation

### Method 1: Load as Unpacked Extension (Recommended for Development)

1. **Download or Clone** this repository to your local machine
2. **Open Chrome/Brave** and navigate to `chrome://extensions/`
3. **Enable Developer Mode** by toggling the switch in the top-right corner
4. **Click "Load unpacked"** and select the folder containing the extension files
5. **Pin the extension** to your toolbar for easy access

### Method 2: Manual Installation

1. Download all files from this repository
2. Create a new folder on your computer
3. Copy all the extension files into this folder:
   - `manifest.json`
   - `content.js`
   - `background.js`
   - `popup.html`
   - `popup.js`
   - `styles.css`
4. Follow steps 2-5 from Method 1

## Usage

### Basic Usage

1. **Activate OCR Mode**:
   - Click the extension icon in your toolbar, then click "Start Selection"
   - Or use the keyboard shortcut: `Ctrl+Shift+O`

2. **Select Area**:
   - Click and drag to draw a selection rectangle or circle
   - The selection area will be highlighted with a blue border
   - Release the mouse button to capture the selected area

3. **Text Extraction**:
   - The extension will automatically capture the selected area
   - OCR processing will extract any text found
   - Extracted text is automatically copied to your clipboard
   - A notification will show the extracted text (first 50 characters)

### Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+Shift+O` | Activate OCR selection mode |
| `Ctrl+Shift+S` | Toggle between rectangle and circle selection |
| `ESC` | Cancel current selection |
| `S` | Change selection shape during active selection |

### Selection Modes

- **Rectangle Mode**: Draw rectangular selections (default)
- **Circle Mode**: Draw circular selections
- Switch between modes using the popup interface or keyboard shortcuts

## Technical Details

### Libraries Used

- **Tesseract.js v4**: OCR engine for text extraction
- **html2canvas v1.4.1**: Screen capture functionality
- Both libraries are loaded from CDN for optimal performance

### Permissions

The extension requires the following permissions:
- `activeTab`: Access to the current tab for content injection
- `clipboardWrite`: Copy extracted text to clipboard
- `storage`: Save user preferences
- `<all_urls>`: Access to all websites for OCR functionality

### Browser Compatibility

- ✅ Chrome (Manifest V3)
- ✅ Brave Browser
- ✅ Microsoft Edge (Chromium-based)
- ✅ Other Chromium-based browsers

## File Structure

```
ocr-screen-capture/
├── manifest.json          # Extension manifest (V3)
├── content.js             # Main content script
├── background.js          # Service worker
├── popup.html            # Extension popup interface
├── popup.js              # Popup functionality
├── styles.css            # Styling for selection overlay
└── README.md             # This file
```

## Troubleshooting

### Common Issues

1. **"Failed to load required libraries"**
   - Check your internet connection
   - Ensure the page allows external script loading
   - Try refreshing the page and activating OCR again

2. **"No text found in selected area"**
   - Make sure the selected area contains readable text
   - Try selecting a larger area
   - Ensure the text has good contrast and is clearly visible

3. **Extension not working on certain pages**
   - Some pages (like chrome:// URLs) don't allow content scripts
   - Try the extension on regular web pages
   - Refresh the page and try again

4. **Clipboard copy failed**
   - Grant clipboard permissions when prompted
   - Try using the keyboard shortcut instead of clicking
   - Check if other applications are blocking clipboard access

### Performance Tips

- Select smaller areas for faster OCR processing
- Ensure good contrast between text and background
- Use rectangle selection for text in rows/columns
- Use circle selection for text in circular arrangements

## Development

### Local Development

1. Make changes to the source files
2. Go to `chrome://extensions/`
3. Click the refresh icon on the extension card
4. Test your changes on a web page

### Adding Features

The extension is built with a modular architecture:
- `content.js`: Main functionality and UI
- `background.js`: Extension lifecycle and messaging
- `popup.js`: User interface controls
- `styles.css`: Visual styling

## Privacy

This extension:
- ✅ Processes all data locally (no data sent to external servers)
- ✅ Only accesses the current tab when activated
- ✅ Does not collect or store personal information
- ✅ Uses CDN libraries for OCR functionality only

## License

This project is open source and available under the MIT License.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## Support

If you encounter any issues or have questions:
1. Check the troubleshooting section above
2. Open an issue on the project repository
3. Provide details about your browser version and the specific problem

---

**Enjoy extracting text from any screen content with OCR Screen Capture!** 🚀
