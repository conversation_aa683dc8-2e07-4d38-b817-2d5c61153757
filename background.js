// Background script for OCR Screen Capture Extension

// Extension installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('OCR Screen Capture Extension installed');
  
  // Set default settings
  chrome.storage.sync.set({
    selectionShape: 'rectangle', // 'rectangle' or 'circle'
    autoActivate: false
  });
});

// Handle keyboard commands
chrome.commands.onCommand.addListener((command) => {
  if (command === 'activate-ocr') {
    // Send message to active tab to activate OCR mode
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { 
          action: 'activateOCR' 
        });
      }
    });
  } else if (command === 'toggle-shape') {
    // Toggle selection shape
    chrome.storage.sync.get(['selectionShape'], (result) => {
      const newShape = result.selectionShape === 'rectangle' ? 'circle' : 'rectangle';
      chrome.storage.sync.set({ selectionShape: newShape });
      
      // Notify active tab of shape change
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, { 
            action: 'shapeChanged',
            shape: newShape
          });
        }
      });
    });
  }
});

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'copyToClipboard') {
    // Copy text to clipboard using the background script
    navigator.clipboard.writeText(request.text).then(() => {
      sendResponse({ success: true });
    }).catch((error) => {
      console.error('Failed to copy to clipboard:', error);
      sendResponse({ success: false, error: error.message });
    });
    return true; // Keep message channel open for async response
  }
  
  if (request.action === 'getSettings') {
    chrome.storage.sync.get(['selectionShape', 'autoActivate'], (result) => {
      sendResponse({
        selectionShape: result.selectionShape || 'rectangle',
        autoActivate: result.autoActivate || false
      });
    });
    return true;
  }
  
  if (request.action === 'updateSettings') {
    chrome.storage.sync.set(request.settings, () => {
      sendResponse({ success: true });
    });
    return true;
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Send message to activate OCR mode
  chrome.tabs.sendMessage(tab.id, { 
    action: 'activateOCR' 
  });
});
