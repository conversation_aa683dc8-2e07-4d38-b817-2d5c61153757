// OCR Screen Capture Content Script

class OCRScreenCapture {
  constructor() {
    this.isActive = false;
    this.isSelecting = false;
    this.startX = 0;
    this.startY = 0;
    this.currentX = 0;
    this.currentY = 0;
    this.selectionShape = 'rectangle';
    this.overlay = null;
    this.selection = null;
    this.instructions = null;

    // Libraries
    this.tesseract = null;
    this.html2canvas = null;

    this.init();
  }

  async init() {
    // Load external libraries
    await this.loadLibraries();

    // Get settings
    await this.loadSettings();

    // Set up event listeners
    this.setupEventListeners();

    console.log('OCR Screen Capture initialized');
  }

  async loadLibraries() {
    try {
      // Load Tesseract.js
      if (!window.Tesseract) {
        const tesseractScript = document.createElement('script');
        tesseractScript.src = 'https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js';
        document.head.appendChild(tesseractScript);

        await new Promise((resolve, reject) => {
          tesseractScript.onload = resolve;
          tesseractScript.onerror = reject;
        });
      }
      this.tesseract = window.Tesseract;

      // Load html2canvas
      if (!window.html2canvas) {
        const html2canvasScript = document.createElement('script');
        html2canvasScript.src = 'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js';
        document.head.appendChild(html2canvasScript);

        await new Promise((resolve, reject) => {
          html2canvasScript.onload = resolve;
          html2canvasScript.onerror = reject;
        });
      }
      this.html2canvas = window.html2canvas;

      console.log('Libraries loaded successfully');
    } catch (error) {
      console.error('Failed to load libraries:', error);
      this.showResult('Failed to load required libraries', true);
    }
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ action: 'getSettings' }, (response) => {
        if (response) {
          this.selectionShape = response.selectionShape;
        }
        resolve();
      });
    });
  }

  setupEventListeners() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'activateOCR') {
        this.activate();
      } else if (request.action === 'shapeChanged') {
        this.selectionShape = request.shape;
        this.showResult(`Selection shape changed to ${request.shape}`, false);
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (this.isActive) {
        if (e.key === 'Escape') {
          this.deactivate();
        } else if (e.key === 's' || e.key === 'S') {
          this.toggleShape();
        }
      }
    });
  }

  activate() {
    if (this.isActive) return;

    this.isActive = true;
    this.createOverlay();
    this.showInstructions();
    document.body.classList.add('ocr-active');

    console.log('OCR mode activated');
  }

  deactivate() {
    if (!this.isActive) return;

    this.isActive = false;
    this.isSelecting = false;
    this.removeOverlay();
    this.hideInstructions();
    document.body.classList.remove('ocr-active');

    console.log('OCR mode deactivated');
  }

  toggleShape() {
    this.selectionShape = this.selectionShape === 'rectangle' ? 'circle' : 'rectangle';
    chrome.runtime.sendMessage({
      action: 'updateSettings',
      settings: { selectionShape: this.selectionShape }
    });
    this.showResult(`Selection shape: ${this.selectionShape}`, false);
  }

  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.className = 'ocr-overlay active';

    // Mouse event handlers
    this.overlay.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.overlay.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.overlay.addEventListener('mouseup', this.handleMouseUp.bind(this));

    document.body.appendChild(this.overlay);
  }

  removeOverlay() {
    if (this.overlay) {
      this.overlay.remove();
      this.overlay = null;
    }
    if (this.selection) {
      this.selection.remove();
      this.selection = null;
    }
  }

  showInstructions() {
    this.instructions = document.createElement('div');
    this.instructions.className = 'ocr-instructions';
    this.instructions.innerHTML = `
      Click and drag to select area • Press 'S' to toggle shape (${this.selectionShape}) • ESC to cancel
    `;
    document.body.appendChild(this.instructions);
  }

  hideInstructions() {
    if (this.instructions) {
      this.instructions.remove();
      this.instructions = null;
    }
  }

  handleMouseDown(e) {
    if (!this.isActive) return;

    this.isSelecting = true;
    this.startX = e.clientX;
    this.startY = e.clientY;
    this.currentX = e.clientX;
    this.currentY = e.clientY;

    this.createSelection();
    e.preventDefault();
  }

  handleMouseMove(e) {
    if (!this.isSelecting) return;

    this.currentX = e.clientX;
    this.currentY = e.clientY;

    this.updateSelection();
    e.preventDefault();
  }

  handleMouseUp(e) {
    if (!this.isSelecting) return;

    this.isSelecting = false;

    // Check if selection is large enough
    const width = Math.abs(this.currentX - this.startX);
    const height = Math.abs(this.currentY - this.startY);

    if (width < 10 || height < 10) {
      this.removeSelection();
      return;
    }

    // Capture and process the selected area
    this.captureSelection();
    e.preventDefault();
  }

  createSelection() {
    this.removeSelection();

    this.selection = document.createElement('div');
    this.selection.className = `ocr-selection ${this.selectionShape}`;
    this.overlay.appendChild(this.selection);

    this.updateSelection();
  }

  updateSelection() {
    if (!this.selection) return;

    const left = Math.min(this.startX, this.currentX);
    const top = Math.min(this.startY, this.currentY);
    const width = Math.abs(this.currentX - this.startX);
    const height = Math.abs(this.currentY - this.startY);

    if (this.selectionShape === 'circle') {
      // For circle, use the larger dimension as diameter
      const diameter = Math.max(width, height);
      const centerX = (this.startX + this.currentX) / 2;
      const centerY = (this.startY + this.currentY) / 2;

      this.selection.style.left = (centerX - diameter / 2) + 'px';
      this.selection.style.top = (centerY - diameter / 2) + 'px';
      this.selection.style.width = diameter + 'px';
      this.selection.style.height = diameter + 'px';
    } else {
      this.selection.style.left = left + 'px';
      this.selection.style.top = top + 'px';
      this.selection.style.width = width + 'px';
      this.selection.style.height = height + 'px';
    }
  }

  removeSelection() {
    if (this.selection) {
      this.selection.remove();
      this.selection = null;
    }
  }

  async captureSelection() {
    try {
      this.showProcessing('Capturing screen...');

      // Get selection bounds
      const rect = this.selection.getBoundingClientRect();

      // Temporarily hide overlay for clean capture
      this.overlay.style.display = 'none';

      // Capture the entire page
      const canvas = await this.html2canvas(document.body, {
        useCORS: true,
        allowTaint: true,
        scale: 1,
        logging: false,
        width: window.innerWidth,
        height: window.innerHeight,
        scrollX: 0,
        scrollY: 0
      });

      // Restore overlay
      this.overlay.style.display = 'block';

      // Create a new canvas for the selected area
      const selectedCanvas = document.createElement('canvas');
      const ctx = selectedCanvas.getContext('2d');

      selectedCanvas.width = rect.width;
      selectedCanvas.height = rect.height;

      // Draw the selected portion
      ctx.drawImage(
        canvas,
        rect.left, rect.top, rect.width, rect.height,
        0, 0, rect.width, rect.height
      );

      // Process with OCR
      await this.processOCR(selectedCanvas);

    } catch (error) {
      console.error('Capture failed:', error);
      this.showResult('Failed to capture screen', true);
    } finally {
      this.hideProcessing();
      this.deactivate();
    }
  }

  async processOCR(canvas) {
    try {
      this.showProcessing('Extracting text...');

      // Convert canvas to image data
      const imageData = canvas.toDataURL('image/png');

      // Initialize Tesseract worker
      const worker = await this.tesseract.createWorker();

      // Perform OCR
      const { data: { text } } = await worker.recognize(imageData);

      // Clean up worker
      await worker.terminate();

      // Process the extracted text
      const cleanText = text.trim();

      if (cleanText) {
        // Copy to clipboard
        await this.copyToClipboard(cleanText);
        this.showResult(`Text copied: "${cleanText.substring(0, 50)}${cleanText.length > 50 ? '...' : ''}"`, false);
      } else {
        this.showResult('No text found in selected area', true);
      }

    } catch (error) {
      console.error('OCR processing failed:', error);
      this.showResult('OCR processing failed', true);
    }
  }

  async copyToClipboard(text) {
    try {
      // Try using the modern clipboard API first
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        return;
      }

      // Fallback to background script for clipboard access
      return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          action: 'copyToClipboard',
          text: text
        }, (response) => {
          if (response && response.success) {
            resolve();
          } else {
            reject(new Error(response?.error || 'Failed to copy to clipboard'));
          }
        });
      });

    } catch (error) {
      console.error('Clipboard copy failed:', error);
      throw error;
    }
  }

  showProcessing(message) {
    this.hideProcessing();

    this.processingElement = document.createElement('div');
    this.processingElement.className = 'ocr-processing';
    this.processingElement.innerHTML = `
      <div class="ocr-spinner"></div>
      <span>${message}</span>
    `;
    document.body.appendChild(this.processingElement);
  }

  hideProcessing() {
    if (this.processingElement) {
      this.processingElement.remove();
      this.processingElement = null;
    }
  }

  showResult(message, isError = false) {
    // Remove any existing result
    const existingResult = document.querySelector('.ocr-result');
    if (existingResult) {
      existingResult.remove();
    }

    const result = document.createElement('div');
    result.className = `ocr-result ${isError ? 'error' : ''}`;
    result.textContent = message;
    document.body.appendChild(result);

    // Auto-remove after 4 seconds
    setTimeout(() => {
      if (result.parentNode) {
        result.remove();
      }
    }, 4000);
  }
}

// Initialize the OCR Screen Capture when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new OCRScreenCapture();
  });
} else {
  new OCRScreenCapture();
}
