<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OCR Screen Capture</title>
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      background: #f8f9fa;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      font-size: 18px;
      margin: 0 0 5px 0;
      color: #333;
    }
    
    .header p {
      font-size: 12px;
      color: #666;
      margin: 0;
    }
    
    .main-button {
      width: 100%;
      padding: 15px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      margin-bottom: 20px;
      transition: background-color 0.2s;
    }
    
    .main-button:hover {
      background: #0056b3;
    }
    
    .main-button:active {
      transform: translateY(1px);
    }
    
    .settings {
      border-top: 1px solid #dee2e6;
      padding-top: 15px;
    }
    
    .setting-group {
      margin-bottom: 15px;
    }
    
    .setting-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    }
    
    .setting-select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 14px;
      background: white;
    }
    
    .shortcuts {
      background: #e9ecef;
      padding: 12px;
      border-radius: 6px;
      font-size: 12px;
      color: #495057;
    }
    
    .shortcuts h3 {
      margin: 0 0 8px 0;
      font-size: 13px;
      font-weight: 600;
    }
    
    .shortcut-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
    }
    
    .shortcut-key {
      font-family: monospace;
      background: #fff;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
    }
    
    .status {
      text-align: center;
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-top: 10px;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>OCR Screen Capture</h1>
    <p>Extract text from any screen area</p>
  </div>
  
  <button id="activateBtn" class="main-button">
    Start Selection
  </button>
  
  <div class="settings">
    <div class="setting-group">
      <label class="setting-label" for="shapeSelect">Selection Shape:</label>
      <select id="shapeSelect" class="setting-select">
        <option value="rectangle">Rectangle</option>
        <option value="circle">Circle</option>
      </select>
    </div>
    
    <div class="shortcuts">
      <h3>Keyboard Shortcuts</h3>
      <div class="shortcut-item">
        <span>Activate OCR</span>
        <span class="shortcut-key">Ctrl+Shift+O</span>
      </div>
      <div class="shortcut-item">
        <span>Toggle Shape</span>
        <span class="shortcut-key">Ctrl+Shift+S</span>
      </div>
      <div class="shortcut-item">
        <span>Cancel Selection</span>
        <span class="shortcut-key">ESC</span>
      </div>
      <div class="shortcut-item">
        <span>Change Shape (during selection)</span>
        <span class="shortcut-key">S</span>
      </div>
    </div>
  </div>
  
  <div id="status" class="status" style="display: none;"></div>
  
  <script src="popup.js"></script>
</body>
</html>
