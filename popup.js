// Popup script for OCR Screen Capture Extension

document.addEventListener('DOMContentLoaded', async () => {
  const activateBtn = document.getElementById('activateBtn');
  const shapeSelect = document.getElementById('shapeSelect');
  const statusDiv = document.getElementById('status');
  
  // Load current settings
  await loadSettings();
  
  // Event listeners
  activateBtn.addEventListener('click', activateOCR);
  shapeSelect.addEventListener('change', updateShape);
  
  async function loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
      if (response) {
        shapeSelect.value = response.selectionShape || 'rectangle';
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }
  
  async function activateOCR() {
    try {
      // Get the active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        showStatus('No active tab found', 'error');
        return;
      }
      
      // Send message to content script
      await chrome.tabs.sendMessage(tab.id, { action: 'activateOCR' });
      
      showStatus('OCR mode activated! Go to the page and start selecting.', 'success');
      
      // Close popup after a short delay
      setTimeout(() => {
        window.close();
      }, 1500);
      
    } catch (error) {
      console.error('Failed to activate OCR:', error);
      showStatus('Failed to activate OCR. Make sure you\'re on a valid web page.', 'error');
    }
  }
  
  async function updateShape() {
    try {
      const newShape = shapeSelect.value;
      
      // Update settings
      await chrome.runtime.sendMessage({
        action: 'updateSettings',
        settings: { selectionShape: newShape }
      });
      
      // Notify active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            action: 'shapeChanged',
            shape: newShape
          });
        } catch (error) {
          // Content script might not be loaded, that's okay
          console.log('Content script not available, settings saved for next activation');
        }
      }
      
      showStatus(`Selection shape changed to ${newShape}`, 'success');
      
    } catch (error) {
      console.error('Failed to update shape:', error);
      showStatus('Failed to update settings', 'error');
    }
  }
  
  function showStatus(message, type = 'success') {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    // Hide after 3 seconds
    setTimeout(() => {
      statusDiv.style.display = 'none';
    }, 3000);
  }
  
  // Handle keyboard shortcuts in popup
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      activateOCR();
    }
  });
});
