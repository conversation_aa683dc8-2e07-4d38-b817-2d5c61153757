<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR Extension Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .text-sample {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .large-text {
            font-size: 24px;
            font-weight: bold;
        }
        .small-text {
            font-size: 12px;
        }
        .colored-text {
            color: #007bff;
            background: #e7f3ff;
            padding: 10px;
        }
        .number-sample {
            font-family: monospace;
            font-size: 18px;
            background: #fff3cd;
            padding: 10px;
            border-radius: 3px;
        }
        .instructions {
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .mixed-content {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .image-placeholder {
            width: 150px;
            height: 100px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>OCR Screen Capture Extension - Test Page</h1>
    
    <div class="instructions">
        <h3>How to Test:</h3>
        <ol>
            <li>Make sure the OCR Screen Capture extension is installed and enabled</li>
            <li>Click the extension icon or press <strong>Ctrl+Shift+O</strong> to activate OCR mode</li>
            <li>Click and drag to select any text area below</li>
            <li>The extracted text should be automatically copied to your clipboard</li>
            <li>Try different text samples and selection shapes!</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>📝 Regular Text Sample</h2>
        <div class="text-sample">
            This is a sample paragraph with regular text that should be easily readable by the OCR engine. 
            The extension should be able to extract this text when you select this area.
        </div>
    </div>

    <div class="test-section">
        <h2>🔢 Numbers and Special Characters</h2>
        <div class="number-sample">
            Phone: +****************<br>
            Email: <EMAIL><br>
            Price: $29.99<br>
            Date: 2024-01-15<br>
            Code: ABC123XYZ
        </div>
    </div>

    <div class="test-section">
        <h2>📏 Different Text Sizes</h2>
        <div class="text-sample large-text">
            LARGE TEXT SAMPLE
        </div>
        <div class="text-sample">
            Normal size text for comparison
        </div>
        <div class="text-sample small-text">
            Small text that might be challenging for OCR
        </div>
    </div>

    <div class="test-section">
        <h2>🎨 Colored Text</h2>
        <div class="colored-text">
            This text has a blue color on a light blue background. 
            OCR should still be able to read this despite the color contrast.
        </div>
    </div>

    <div class="test-section">
        <h2>🖼️ Mixed Content</h2>
        <div class="mixed-content">
            <div class="image-placeholder">
                Image Area
            </div>
            <div>
                <h3>Text Next to Image</h3>
                <p>This text is positioned next to a colored block. Try selecting just the text, just the image area, or both together to see how the OCR handles different content types.</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 Multi-line Text</h2>
        <div class="text-sample">
            Line 1: First line of text<br>
            Line 2: Second line of text<br>
            Line 3: Third line of text<br>
            Line 4: Fourth line of text
        </div>
    </div>

    <div class="test-section">
        <h2>🔤 Mixed Case and Formatting</h2>
        <div class="text-sample">
            <strong>UPPERCASE TEXT</strong><br>
            <em>lowercase text</em><br>
            <u>Underlined Text</u><br>
            Normal Text<br>
            <code>Code Text: console.log("Hello World");</code>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Test Results</h2>
        <p>After testing the extension with the samples above, you should be able to:</p>
        <ul>
            <li>✓ Select text areas with both rectangle and circle shapes</li>
            <li>✓ Extract text from different font sizes and styles</li>
            <li>✓ Copy extracted text to clipboard automatically</li>
            <li>✓ See visual feedback during selection</li>
            <li>✓ Handle numbers, special characters, and mixed content</li>
        </ul>
    </div>

    <script>
        // Add some dynamic content for testing
        document.addEventListener('DOMContentLoaded', function() {
            const timestamp = new Date().toLocaleString();
            const dynamicSection = document.createElement('div');
            dynamicSection.className = 'test-section';
            dynamicSection.innerHTML = `
                <h2>⏰ Dynamic Content</h2>
                <div class="text-sample">
                    Page loaded at: ${timestamp}<br>
                    Random number: ${Math.floor(Math.random() * 10000)}
                </div>
            `;
            document.body.appendChild(dynamicSection);
        });
    </script>
</body>
</html>
